# 🚀 Revisa AI

A modern full-stack monorepo built with NestJS, React Router, and TurboRepo.

## 🏗️ Architecture

```
revisa-ai/
├── 📱 apps/
│   ├── server/          # NestJS API (TypeScript)
│   └── www/             # React Router Frontend (TypeScript)
├── 📦 packages/
│   ├── eslint-config/   # Shared ESLint configurations
│   └── typescript-config/ # Shared TypeScript configurations
└── 🐳 Docker/           # Production deployment setup
```

## ⚡ Quick Start

### Prerequisites

- Node.js 20+
- npm 10+

### Development Setup

```bash
# Clone and install
git clone <your-repo>
cd revisa-ai
npm install

# Start development (both apps with hot reload)
npm run dev

# Individual apps
npm run dev:server    # NestJS API on :3000
npm run dev:www       # React Router on :5173
```

### Available Scripts

```bash
npm run dev          # Start both apps in development
npm run build        # Build all apps for production
npm run lint         # Lint all code
npm run format       # Format code with Prettier
npm run test         # Run all tests
npm run typecheck    # TypeScript type checking
```

## 🔧 Development Workflow

### Daily Development

```bash
# Start both apps (recommended)
npm run dev

# Or start individual apps
npm run dev:server   # Just NestJS API on :3000
npm run dev:www      # Just React Router on :5173

# In separate terminals (optional)
npm run lint         # Check code quality
npm run typecheck    # Check types
npm run test         # Run tests
```

### Testing Individual Apps

#### Server API Testing

```bash
# Start server
npm run dev:server

# Test endpoints (in another terminal)
curl http://localhost:3000                    # Hello World!
curl http://localhost:3000/api/health         # Health check (if implemented)

# Or use any API client:
# - Postman
# - Insomnia
# - VS Code REST Client
```

#### Frontend Testing

```bash
# Start frontend
npm run dev:www

# Open browser
open http://localhost:5173
```

#### Docker Testing (Individual Apps)

```bash
# Test server only
docker build -f apps/server/Dockerfile -t revisa-server .
docker run -p 3000:3000 revisa-server

# Test frontend only
docker build -f apps/www/Dockerfile -t revisa-www .
docker run -p 3000:3000 revisa-www

# Test with curl
curl http://localhost:3000
```

### Development vs Production

| Aspect           | Development             | Production                         |
| ---------------- | ----------------------- | ---------------------------------- |
| **Server URL**   | `http://localhost:3000` | `http://localhost/api` (via Nginx) |
| **Frontend URL** | `http://localhost:5173` | `http://localhost` (via Nginx)     |
| **Hot Reload**   | ✅ Enabled              | ❌ Static builds                   |
| **Source Maps**  | ✅ Full debugging       | ❌ Optimized                       |
| **Build Time**   | ⚡ Instant              | 🐌 5-7 minutes                     |
| **Testing**      | `curl localhost:3000`   | `curl localhost/api`               |

### Code Quality

- **ESLint**: Shared configs across all apps
- **Prettier**: Consistent code formatting
- **TypeScript**: Full type safety
- **TurboRepo**: Optimized build caching

## 🚀 Production Deployment

### Docker Compose (Recommended)

```bash
# Build and deploy production
sudo ./scripts/docker-prod.sh deploy

# Access your application
# Frontend: http://localhost
# API: http://localhost/api
```

### Manual Docker Build

```bash
# Build production images
docker compose -f docker-compose.prod.yml build

# Start production services
docker compose -f docker-compose.prod.yml up -d

# Stop services
docker compose -f docker-compose.prod.yml down
```

### Production Management

```bash
# Available production commands
sudo ./scripts/docker-prod.sh deploy    # Build and deploy
sudo ./scripts/docker-prod.sh status    # Check health
sudo ./scripts/docker-prod.sh logs      # View logs
sudo ./scripts/docker-prod.sh stop      # Stop services
sudo ./scripts/docker-prod.sh cleanup   # Clean up
```

## 🌐 Deployment Options

### Cloud Platforms

- **Railway.app** - Monorepo support, auto-deploy from Git
- **Render.com** - Docker Compose support
- **DigitalOcean** - App Platform or Droplets
- **Heroku** - Deploy apps individually
- **AWS/GCP/Azure** - Container services

### Environment Variables

Create `.env` file in root:

```env
# Server Configuration
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://...

# Frontend Configuration
VITE_API_URL=https://your-api-domain.com
```

## 📁 Project Structure

### Apps

- **`apps/server/`** - NestJS REST API
  - Modern Node.js backend
  - TypeScript with decorators
  - Built-in validation and testing
- **`apps/www/`** - React Router Frontend
  - React 19 with React Router 7
  - TailwindCSS for styling
  - Vite for fast builds

### Packages

- **`packages/eslint-config/`** - Shared linting rules
- **`packages/typescript-config/`** - Shared TypeScript settings

## 🛠️ Tech Stack

### Frontend

- **React 19** - Latest React with concurrent features
- **React Router 7** - File-based routing
- **TailwindCSS 4** - Utility-first CSS framework
- **Vite 6** - Fast build tool and dev server

### Backend

- **NestJS 11** - Progressive Node.js framework
- **TypeScript 5** - Type-safe JavaScript
- **Express** - Web application framework
- **Jest** - Testing framework

### DevOps

- **TurboRepo** - Monorepo build system
- **Docker** - Containerization
- **Nginx** - Reverse proxy and load balancer
- **ESLint + Prettier** - Code quality tools

## 🧪 Testing

```bash
# Run all tests
npm run test

# Test specific app
cd apps/server && npm run test
cd apps/www && npm run test

# Test with coverage
cd apps/server && npm run test:cov
```

## 📊 Performance

### Development

- **Hot reload** - Instant updates during development
- **TurboRepo caching** - Skip unchanged builds
- **Fast builds** - Vite (frontend) + SWC (backend)

### Production

- **Multi-stage Docker** - Optimized container images
- **Nginx optimization** - Gzip, caching, security headers
- **Health checks** - Automatic service monitoring

## 🔒 Security

- **Non-root containers** - Enhanced security
- **Security headers** - XSS, CORS, CSP protection
- **Environment isolation** - Separate dev/prod configs
- **Dependency scanning** - Automated vulnerability checks

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch: `git checkout -b feature/amazing-feature`
3. **Commit** changes: `git commit -m 'Add amazing feature'`
4. **Push** to branch: `git push origin feature/amazing-feature`
5. **Open** a Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

**Development server won't start:**

```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

**Docker build fails:**

```bash
# Clean Docker cache
docker system prune -f
sudo ./scripts/docker-prod.sh cleanup
```

**Port conflicts:**

```bash
# Check what's using ports
lsof -i :3000
lsof -i :5173
```

### Getting Help

- Check the [Issues](../../issues) page
- Review [Docker documentation](./README-Docker.md)
- Check [Deployment guide](./DEPLOYMENT.md)
