{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "apps/server/Dockerfile", "watchPatterns": ["apps/server/**", "packages/**", "prisma/**", "package.json", "package-lock.json", "turbo.json"]}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "startCommand": "npx prisma migrate deploy && node dist/main.js", "healthcheckPath": "/", "healthcheckTimeout": 100}}