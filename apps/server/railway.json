{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "apps/server/Dockerfile", "buildContext": ".", "watchPatterns": ["apps/server/**", "packages/**", "package.json", "package-lock.json", "turbo.json"]}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "startCommand": "node dist/main.js", "healthcheckPath": "/", "healthcheckTimeout": 100}}