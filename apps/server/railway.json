{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "apps/server/Dockerfile", "watchPatterns": ["apps/server/**", "packages/**", "prisma/**", "package.json", "package-lock.json", "turbo.json"]}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "startCommand": "echo 'Starting migrations...' && npx prisma migrate deploy --schema=./prisma/schema.prisma && echo 'Migrations complete. Starting server...' && echo 'Current directory:' && pwd && echo 'Files in current directory:' && ls -la && echo 'Files in dist:' && ls -la dist/ && echo 'Starting Node.js server...' && node dist/main.js", "healthcheckPath": "/", "healthcheckTimeout": 100}}