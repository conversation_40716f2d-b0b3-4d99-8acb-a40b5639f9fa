{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "apps/www/Dockerfile", "buildContext": ".", "watchPatterns": ["apps/www/**", "packages/**", "package.json", "package-lock.json", "turbo.json"]}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "startCommand": "npm run start", "healthcheckPath": "/", "healthcheckTimeout": 100}}