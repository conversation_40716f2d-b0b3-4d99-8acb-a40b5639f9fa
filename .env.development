# =============================================================================
# REVISA.AI ENVIRONMENT CONFIGURATION (Deploy-Agnostic)
# =============================================================================
# Copy this file to .env.development, .env.staging, .env.production
# and fill in the appropriate values for each environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
PORT=3001
APP_NAME=revisa-ai
APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION (Deploy-Agnostic)
# =============================================================================
# Development: Uses Docker Compose PostgreSQL
# Production: Uses managed database service (Railway, AWS RDS, etc.)
DATABASE_URL=postgresql://revisa_user:revisa_password@localhost:5432/revisa_dev

# =============================================================================
# REDIS CONFIGURATION (Deploy-Agnostic)
# =============================================================================
# Development: Uses Docker Compose Redis
# Production: Uses managed Redis service (Railway, AWS ElastiCache, etc.)
REDIS_URL=redis://:revisa_redis_password@localhost:6379

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
JWT_SECRET=dev-secret-key-12345-not-for-production
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# =============================================================================
# LLM PROVIDER CONFIGURATION (OPTIONAL - Skip for now!)
# =============================================================================
# OpenAI Configuration (Uncomment when you need LLM features)
# OPENAI_API_KEY=sk-your-openai-api-key
# OPENAI_ORG_ID=org-your-organization-id
# OPENAI_PROJECT_ID=proj_your-project-id

# OpenRouter Configuration (Alternative Provider)
# OPENROUTER_API_KEY=sk-or-your-openrouter-key
# OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Azure OpenAI Configuration (Enterprise)
# AZURE_OPENAI_API_KEY=your-azure-openai-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
# AZURE_OPENAI_API_VERSION=2024-02-15-preview

# =============================================================================
# RATE LIMITING & QUOTAS
# =============================================================================
# User tier-based rate limits (requests per minute)
RATE_LIMIT_FREE_TIER=10
RATE_LIMIT_PRO_TIER=100
RATE_LIMIT_ENTERPRISE_TIER=1000

# API quota limits (tokens per month)
QUOTA_FREE_TIER=100000
QUOTA_PRO_TIER=1000000
QUOTA_ENTERPRISE_TIER=10000000

# =============================================================================
# MONITORING & OBSERVABILITY (OPTIONAL - Skip for now!)
# =============================================================================
# Sentry Error Tracking (Uncomment when you need error tracking)
# SENTRY_DSN=https://<EMAIL>/project-id

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# =============================================================================
# EMAIL CONFIGURATION (OPTIONAL - Skip for now!)
# =============================================================================
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
# FROM_EMAIL=<EMAIL>

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
# API Base URL (Environment Specific)
VITE_API_URL=http://localhost:3000
VITE_WS_URL=ws://localhost:3001

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_CHAT_EXPORT=true
VITE_ENABLE_DARK_MODE=true

# =============================================================================
# DEPLOYMENT SPECIFIC (OPTIONAL - Skip for now!)
# =============================================================================
# Railway Specific
RAILWAY_ENVIRONMENT=development

# AWS Specific (if deploying to AWS)
# AWS_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# Docker Specific
# DOCKER_REGISTRY=your-registry.com
# DOCKER_IMAGE_TAG=latest

# =============================================================================
# DEVELOPMENT ONLY
# =============================================================================
# Database seeding
SEED_DATABASE=true
SEED_ADMIN_EMAIL=<EMAIL>
SEED_ADMIN_PASSWORD=admin123

# Debug settings
DEBUG_SQL=false
DEBUG_REDIS=false
ENABLE_PLAYGROUND=true
