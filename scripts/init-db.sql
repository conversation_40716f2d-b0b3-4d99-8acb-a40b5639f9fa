-- =============================================================================
-- REVISA.AI DATABASE INITIALIZATION SCRIPT
-- =============================================================================
-- This script initializes the development database with basic schema
-- Production databases should use Prisma migrations instead

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create development user if not exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'revisa_user') THEN
        CREATE ROLE revisa_user WITH LOGIN PASSWORD 'revisa_password';
    END IF;
END
$$;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE revisa_dev TO revisa_user;
GRANT ALL ON SCHEMA public TO revisa_user;

-- Create basic tables (will be replaced by Prisma migrations)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    tier VARCHAR(50) DEFAULT 'free',
    api_quota INTEGER DEFAULT 100000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS threads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    model VARCHAR(100) DEFAULT 'gpt-4o-mini',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    thread_id UUID NOT NULL REFERENCES threads(id) ON DELETE CASCADE,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    tokens INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS api_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    tokens INTEGER NOT NULL,
    cost DECIMAL(10, 6) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_threads_user_id ON threads(user_id);
CREATE INDEX IF NOT EXISTS idx_threads_created_at ON threads(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_thread_id ON messages(thread_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_api_usage_user_id ON api_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_created_at ON api_usage(created_at);

-- Insert seed data for development
INSERT INTO users (email, name, password_hash, tier) VALUES 
    ('<EMAIL>', 'Admin User', crypt('admin123', gen_salt('bf')), 'enterprise'),
    ('<EMAIL>', 'Test User', crypt('test123', gen_salt('bf')), 'free')
ON CONFLICT (email) DO NOTHING;

-- Create a sample thread and messages for testing
DO $$
DECLARE
    admin_user_id UUID;
    test_thread_id UUID;
BEGIN
    SELECT id INTO admin_user_id FROM users WHERE email = '<EMAIL>';
    
    INSERT INTO threads (id, user_id, title) VALUES 
        (uuid_generate_v4(), admin_user_id, 'Welcome to Revisa AI')
    RETURNING id INTO test_thread_id;
    
    INSERT INTO messages (thread_id, role, content, tokens) VALUES 
        (test_thread_id, 'user', 'Hello! How can you help me today?', 12),
        (test_thread_id, 'assistant', 'Hello! I''m Revisa AI, your intelligent assistant. I can help you with a wide variety of tasks including answering questions, writing, analysis, coding, and much more. What would you like to work on today?', 45);
END
$$;
