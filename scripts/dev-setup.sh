#!/bin/bash

# =============================================================================
# REVISA.AI DEVELOPMENT SETUP SCRIPT
# =============================================================================
# This script sets up the development environment with containerized databases
# while keeping the setup deploy-agnostic for future platform migrations

set -e

echo "🚀 Setting up Revisa.AI development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed and running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is installed and running"
}

# Check if Docker Compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    print_success "Docker Compose is available"
}

# Create environment file if it doesn't exist
setup_env_file() {
    print_status "Setting up environment file..."
    
    if [ ! -f .env.development ]; then
        print_status "Creating .env.development from template..."
        cp .env.example .env.development
        print_warning "Please update .env.development with your actual values"
    else
        print_success ".env.development already exists"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    if [ ! -d "node_modules" ]; then
        npm install
        print_success "Dependencies installed"
    else
        print_success "Dependencies already installed"
    fi
}

# Start development services
start_services() {
    print_status "Starting development services..."
    
    # Stop any existing services
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    
    # Start database services first
    print_status "Starting PostgreSQL and Redis..."
    docker-compose -f docker-compose.dev.yml up -d postgres redis
    
    # Wait for databases to be ready
    print_status "Waiting for databases to be ready..."
    sleep 10
    
    # Check if databases are healthy
    if docker-compose -f docker-compose.dev.yml ps postgres | grep -q "healthy"; then
        print_success "PostgreSQL is ready"
    else
        print_warning "PostgreSQL might not be fully ready yet"
    fi
    
    if docker-compose -f docker-compose.dev.yml ps redis | grep -q "healthy"; then
        print_success "Redis is ready"
    else
        print_warning "Redis might not be fully ready yet"
    fi
}

# Display connection information
show_connection_info() {
    print_success "Development environment is ready!"
    echo ""
    echo "📊 Database Connection Info:"
    echo "  PostgreSQL: postgresql://revisa_user:revisa_password@localhost:5432/revisa_dev"
    echo "  Redis: redis://:revisa_redis_password@localhost:6379"
    echo ""
    echo "🛠️  Development Commands:"
    echo "  Start all services:     npm run dev"
    echo "  Start server only:      npm run dev:server"
    echo "  Start frontend only:    npm run dev:www"
    echo "  Stop databases:         docker-compose -f docker-compose.dev.yml down"
    echo ""
    echo "🔗 URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend:  http://localhost:3001"
    echo "  API Test: http://localhost:3001/api/test"
    echo ""
    echo "📝 Next Steps:"
    echo "  1. Update .env.development with your API keys"
    echo "  2. Run 'npm run dev' to start the application"
    echo "  3. Visit http://localhost:3000 to see your app"
}

# Main execution
main() {
    echo "🎯 Revisa.AI Development Setup"
    echo "=============================="
    
    check_docker
    check_docker_compose
    setup_env_file
    install_dependencies
    start_services
    show_connection_info
    
    print_success "Setup complete! Happy coding! 🎉"
}

# Run main function
main "$@"
