{"name": "revisa-ai", "private": true, "packageManager": "npm@10.9.2", "workspaces": ["apps/*", "packages/*"], "devDependencies": {"prettier": "^3.4.2", "prisma": "^6.11.1", "turbo": "^2.5.4", "typescript": "^5.8.2"}, "scripts": {"dev": "turbo run dev start:dev --parallel", "dev:server": "cd apps/server && npm run start:dev", "dev:www": "cd apps/www && npm run dev", "build": "turbo run build", "start": "cd apps/server && node dist/main.js", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,md,json}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,md,json}\"", "test": "turbo run test", "typecheck": "turbo run typecheck", "setup:dev": "./scripts/dev-setup.sh", "db:up": "docker compose -f docker-compose.dev.yml up -d postgres redis", "db:down": "docker compose -f docker-compose.dev.yml down", "db:logs": "docker compose -f docker-compose.dev.yml logs -f postgres redis", "db:migrate": "npx prisma migrate dev", "db:reset": "npx prisma migrate reset", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "docker:dev": "docker compose -f docker-compose.dev.yml up", "docker:dev:build": "docker compose -f docker-compose.dev.yml up --build", "docker:prod": "docker compose -f docker-compose.prod.yml up -d", "docker:prod:build": "docker compose -f docker-compose.prod.yml up -d --build"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@prisma/client": "^6.11.1"}}